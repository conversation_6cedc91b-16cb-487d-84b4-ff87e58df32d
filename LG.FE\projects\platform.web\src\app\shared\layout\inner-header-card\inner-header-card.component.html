<div class="surface-card shadow-0">
    <div class="py-3 px-3">
        <!-- Mobile Layout -->
        <div class="mobile-header-layout">
            @if (isBackButton && canShowActionButton) {
            <div class="mobile-back-button">
                <p-button (click)="onActionButtonClick()"
                    styleClass="p-button-text p-button-rounded mobile-back-btn"
                    [icon]="actionButtonIcon || 'pi pi-arrow-left'"
                    [ariaLabel]="actionButtonLabel || 'Go back'">
                </p-button>
            </div>
            }
            <div class="mobile-header-content">
                <div class="flex flex-column font-semibold text-lg primary-purple-color">
                    <ng-content select="[breadcrumb-label]"></ng-content>
                    <app-breadcrumbs [breadcrumbs]="breadcrumbs"></app-breadcrumbs>
                </div>
            </div>
            @if (!isBackButton && actionButtonLabel && canShowActionButton) {
            <div class="mobile-action-button">
                <p-button (click)="onActionButtonClick()" [label]="actionButtonLabel"
                    styleClass="{{actionButtonClass}} mobile-action-btn" [rounded]="true"
                    [icon]="actionButtonIcon" iconPos="left">
                </p-button>
            </div>
            }
            <ng-content select="[action-content]"></ng-content>
        </div>

        <!-- Desktop Layout -->
        <div class="desktop-header-layout">
            <div class="flex flex-row align-items-center justify-content-between">
                <div class="flex flex-column font-semibold text-lg md:text-xl primary-purple-color">
                    <ng-content select="[breadcrumb-label]"></ng-content>
                    <app-breadcrumbs [breadcrumbs]="breadcrumbs"></app-breadcrumbs>
                </div>
                <div class="md:mt-0">
                    @if (actionButtonLabel && canShowActionButton) {
                    <p-button (click)="onActionButtonClick()" [label]="actionButtonLabel"
                        styleClass="{{actionButtonClass}} w-full" [rounded]="true" [icon]="actionButtonIcon" iconPos="left">
                    </p-button>
                    }
                    <ng-content select="[action-content]"></ng-content>
                </div>
            </div>
        </div>
    </div>
</div>