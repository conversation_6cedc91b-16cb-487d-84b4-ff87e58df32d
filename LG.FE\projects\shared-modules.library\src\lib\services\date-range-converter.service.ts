import { Injectable, inject } from '@angular/core';
import { TimezoneService, DateInput } from './timezone.service';

/**
 * Configuration options for date range conversion
 */
export interface IDateRangeConversionOptions {
  /** Source timezone for the input dates (defaults to user's current timezone) */
  sourceTimezone?: string;
  /** Whether to apply start of day to the 'from' date (defaults to true) */
  applyStartOfDay?: boolean;
  /** Whether to apply end of day to the 'to' date (defaults to true) */
  applyEndOfDay?: boolean;
  /** Output format: 'date' returns Date objects, 'string' returns ISO strings (defaults to 'date') */
  outputFormat?: 'date' | 'string';
}

/**
 * Result of date range conversion
 */
export interface IDateRangeConversionResult<T extends 'date' | 'string' = 'date'> {
  /** Converted 'from' date in UTC */
  fromDate: T extends 'date' ? Date | null : string | null;
  /** Converted 'to' date in UTC */
  toDate: T extends 'date' ? Date | null : string | null;
}

/**
 * Service for converting date ranges to UTC with proper timezone handling
 * 
 * This service provides generic utilities for converting date ranges (like approvedDateFrom/approvedDateTo)
 * from local timezone to UTC, with proper start-of-day and end-of-day handling.
 * 
 * @example
 * ```typescript
 * // Convert date range to UTC for API request
 * const result = this.dateRangeConverter.convertDateRangeToUtc(
 *   request.approvedDateFrom,
 *   request.approvedDateTo,
 *   { outputFormat: 'string' }
 * );
 * 
 * // Update request with converted dates
 * request.approvedDateFrom = result.fromDate;
 * request.approvedDateTo = result.toDate;
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class DateRangeConverterService {
  private readonly timezoneService = inject(TimezoneService);

  /**
   * Converts a date range from local timezone to UTC with proper day boundary handling
   * 
   * @param fromDate - The start date of the range (can be Date, string, null, or undefined)
   * @param toDate - The end date of the range (can be Date, string, null, or undefined)
   * @param options - Configuration options for the conversion
   * @returns Object containing the converted UTC dates
   * 
   * @example
   * ```typescript
   * // Basic usage - converts to UTC Date objects with start/end of day
   * const result = this.dateRangeConverter.convertDateRangeToUtc(
   *   new Date('2024-01-15'),
   *   new Date('2024-01-20')
   * );
   * // result.fromDate: 2024-01-15 00:00:00 UTC
   * // result.toDate: 2024-01-20 23:59:59 UTC
   * 
   * // Convert to ISO strings for API
   * const apiResult = this.dateRangeConverter.convertDateRangeToUtc(
   *   fromDate,
   *   toDate,
   *   { outputFormat: 'string' }
   * );
   * 
   * // Custom timezone and no day boundary adjustment
   * const customResult = this.dateRangeConverter.convertDateRangeToUtc(
   *   fromDate,
   *   toDate,
   *   {
   *     sourceTimezone: 'America/New_York',
   *     applyStartOfDay: false,
   *     applyEndOfDay: false
   *   }
   * );
   * ```
   */
  convertDateRangeToUtc<T extends 'date' | 'string'>(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined,
    options: IDateRangeConversionOptions & { outputFormat: T }
  ): IDateRangeConversionResult<T>;
  convertDateRangeToUtc(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined,
    options?: IDateRangeConversionOptions
  ): IDateRangeConversionResult<'date'>;
  convertDateRangeToUtc<T extends 'date' | 'string'>(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined,
    options: IDateRangeConversionOptions & { outputFormat?: T } = {}
  ): IDateRangeConversionResult<T> {
    const {
      sourceTimezone,
      applyStartOfDay = true,
      applyEndOfDay = true,
      outputFormat = 'date' as T
    } = options;

    // Convert fromDate (start of range)
    let convertedFromDate: Date | string | null = null;
    if (fromDate != null) {
      try {
        convertedFromDate = this.timezoneService.convertLocalToUtc(fromDate, {
          sourceTimezone,
          outputFormat,
          dayBoundary: applyStartOfDay ? 'start' : undefined
        });
      } catch (error) {
        console.warn('Failed to convert fromDate:', fromDate, error);
        convertedFromDate = null;
      }
    }

    // Convert toDate (end of range)
    let convertedToDate: Date | string | null = null;
    if (toDate != null) {
      try {
        convertedToDate = this.timezoneService.convertLocalToUtc(toDate, {
          sourceTimezone,
          outputFormat,
          dayBoundary: applyEndOfDay ? 'end' : undefined
        });
      } catch (error) {
        console.warn('Failed to convert toDate:', toDate, error);
        convertedToDate = null;
      }
    }

    return {
      fromDate: convertedFromDate as any,
      toDate: convertedToDate as any
    };
  }

  /**
   * Converts a date range from UTC to local timezone for display purposes
   * 
   * @param fromDate - The UTC start date
   * @param toDate - The UTC end date
   * @param options - Configuration options for the conversion
   * @returns Object containing the converted local dates
   * 
   * @example
   * ```typescript
   * // Convert UTC dates back to local for display
   * const displayResult = this.dateRangeConverter.convertDateRangeFromUtc(
   *   utcFromDate,
   *   utcToDate,
   *   { outputFormat: 'date' }
   * );
   * ```
   */
  convertDateRangeFromUtc<T extends 'date' | 'string'>(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined,
    options: { targetTimezone?: string; outputFormat: T }
  ): IDateRangeConversionResult<T>;
  convertDateRangeFromUtc(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined,
    options?: { targetTimezone?: string; outputFormat?: 'date' }
  ): IDateRangeConversionResult<'date'>;
  convertDateRangeFromUtc<T extends 'date' | 'string'>(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined,
    options: { targetTimezone?: string; outputFormat?: T } = {}
  ): IDateRangeConversionResult<T> {
    const { targetTimezone, outputFormat = 'date' as T } = options;

    // Convert fromDate from UTC to local
    let convertedFromDate: Date | string | null = null;
    if (fromDate != null) {
      try {
        convertedFromDate = this.timezoneService.convertUtcToLocal(fromDate, {
          timezone: targetTimezone,
          outputFormat
        });
      } catch (error) {
        console.warn('Failed to convert fromDate from UTC:', fromDate, error);
        convertedFromDate = null;
      }
    }

    // Convert toDate from UTC to local
    let convertedToDate: Date | string | null = null;
    if (toDate != null) {
      try {
        convertedToDate = this.timezoneService.convertUtcToLocal(toDate, {
          timezone: targetTimezone,
          outputFormat
        });
      } catch (error) {
        console.warn('Failed to convert toDate from UTC:', toDate, error);
        convertedToDate = null;
      }
    }

    return {
      fromDate: convertedFromDate as any,
      toDate: convertedToDate as any
    };
  }

  /**
   * Helper method to clean and convert date range properties in a request object
   * This is useful for API request preparation where you want to convert specific date range fields
   * 
   * @param request - The request object containing date range properties
   * @param fromFieldName - Name of the 'from' date field in the request
   * @param toFieldName - Name of the 'to' date field in the request
   * @param options - Configuration options for the conversion
   * @returns A new object with converted date range fields
   * 
   * @example
   * ```typescript
   * // Convert approvedDateFrom/approvedDateTo in a teachers request
   * const cleanedRequest = this.dateRangeConverter.convertDateRangeInRequest(
   *   teachersRequest,
   *   'approvedDateFrom',
   *   'approvedDateTo',
   *   { outputFormat: 'string' }
   * );
   * ```
   */
  convertDateRangeInRequest<T extends Record<string, any>>(
    request: T,
    fromFieldName: keyof T,
    toFieldName: keyof T,
    options: IDateRangeConversionOptions = {}
  ): T {
    const fromDate = request[fromFieldName] as DateInput | null | undefined;
    const toDate = request[toFieldName] as DateInput | null | undefined;

    const converted = this.convertDateRangeToUtc(fromDate, toDate, {
      ...options,
      outputFormat: options.outputFormat || 'string'
    } as any);

    return {
      ...request,
      [fromFieldName]: converted.fromDate,
      [toFieldName]: converted.toDate
    };
  }

  /**
   * Validates that a date range is logically correct (fromDate <= toDate)
   * 
   * @param fromDate - The start date
   * @param toDate - The end date
   * @returns True if the range is valid, false otherwise
   */
  validateDateRange(
    fromDate: DateInput | null | undefined,
    toDate: DateInput | null | undefined
  ): boolean {
    if (!fromDate || !toDate) {
      return true; // Allow partial ranges
    }

    try {
      const from = new Date(fromDate);
      const to = new Date(toDate);
      return from <= to;
    } catch (error) {
      console.warn('Invalid date format in range validation:', { fromDate, toDate });
      return false;
    }
  }
}
