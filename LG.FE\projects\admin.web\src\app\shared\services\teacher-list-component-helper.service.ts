// teacher-list-component-helper.service.ts
import { Injectable } from '@angular/core';
import { Table } from 'primeng/table';
import { ISearchTeacherDto, ISpeakingLanguageDto, ITeacherTeachingLanguageDto } from 'SharedModules.Library';
import { inject } from '@angular/core';

import {
  IGetTeachersResponse,
  EnumDropdownOptionsService,
  IDataGridFields,
  nameOf,
  IGetTeachersRequest,
  IGenderEnum,
} from "SharedModules.Library";
import moment from 'moment-timezone';
import { Params } from "@angular/router";
import { CsvExportService } from './csv-export.service';

const ISearchTeacherDtoParamsMap = nameOf<ISearchTeacherDto>();

@Injectable({
  providedIn: 'root'
})
export class TeacherListComponentHelperService {
  enumDropdownOptionsService = inject(EnumDropdownOptionsService);
  csvExportService = inject(CsvExportService);
  // Default sort configuration - centralized in one place
  public static readonly DEFAULT_SORT_COLUMN = ISearchTeacherDtoParamsMap.firstName;
  public static readonly DEFAULT_SORT_DIRECTION = 'asc';

  constructor() {
  }

  // Getter methods for accessing default sort configuration
  getDefaultSortColumn(): string {
    return TeacherListComponentHelperService.DEFAULT_SORT_COLUMN;
  }

  getDefaultSortDirection(): string {
    return TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION;
  }

  exportTeacherTable(table: Table, cols: IDataGridFields[], teachersResponse: IGetTeachersResponse) {
    if (!table || !teachersResponse.pageData || teachersResponse.pageData.length === 0) {
      return;
    }

    // Format the teacher data for export using the generic CSV service
    const formattedData = this.csvExportService.formatDataForExport(
      teachersResponse.pageData,
      cols,
      (teacher, columns) => this.formatTeacherForExport(teacher, columns)
    );

    // Export using the generic CSV service
    this.csvExportService.exportToCSV(formattedData, cols, {
      filename: `teachers_export_${moment().format('YYYY-MM-DD_HH-mm-ss')}.csv`
    });
  }

  /**
   * Formats a single teacher record for CSV export
   * This contains all the teacher-specific formatting logic
   */
  private formatTeacherForExport(teacher: ISearchTeacherDto, cols: IDataGridFields[]): Record<string, string> {
    const exportObject: Record<string, string> = {};
    const teacherFields = nameOf<ISearchTeacherDto>();

    // Initialize all column fields to empty strings to ensure consistent structure
    for (const col of cols) {
      exportObject[col.field] = '';
    }

    // Process each property explicitly based on field names from cols
    for (const col of cols) {
      const fieldName = col.field;

      if (fieldName === teacherFields.firstName) {
        exportObject[fieldName] = teacher.firstName || '';
      }
      else if (fieldName === teacherFields.lastName) {
        exportObject[fieldName] = teacher.lastName || '';
      }
      else if (fieldName === teacherFields.primaryEmail) {
        exportObject[fieldName] = teacher.primaryEmail || '';
      }
      else if (fieldName === teacherFields.gender) {
        exportObject[fieldName] = teacher.gender === 'Male' ? 'Male' : 'Female';
      }
      else if (fieldName === teacherFields.country) {
        exportObject[fieldName] = teacher.country || '';
      }
      else if (fieldName === teacherFields.isBlocked) {
        exportObject[fieldName] = teacher.isBlocked ? 'Blocked' : 'Active';
      }
      else if (fieldName === teacherFields.availabilityStatus) {
        if (teacher.availabilityStatus !== undefined) {
          try {
            const availabilityOptions = this.enumDropdownOptionsService.teacherAvailabilityStatusOptions;
            const availabilityValue = this.enumDropdownOptionsService.getLabelFromValue(availabilityOptions, teacher.availabilityStatus);
            exportObject[fieldName] = typeof availabilityValue === 'string' ? availabilityValue : String(availabilityValue || '');
          } catch (error) {
            console.warn('Error getting availability status label:', error);
            exportObject[fieldName] = String(teacher.availabilityStatus || '');
          }
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.lastAvailStatusUpdate) {
        exportObject[fieldName] = teacher.lastAvailStatusUpdate ? new Date(teacher.lastAvailStatusUpdate).toLocaleDateString() : '';
      }
      else if (fieldName === teacherFields.speakingLanguages) {
        if (Array.isArray(teacher.speakingLanguages) && teacher.speakingLanguages.length > 0) {
          exportObject[fieldName] = teacher.speakingLanguages
            .map((item: ISpeakingLanguageDto) => {
              if (item && typeof item === 'object' && item.language) {
                return item.language;
              }
              return typeof item === 'string' ? item : String(item);
            })
            .filter(lang => lang)
            .join(' | ');
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.teacherTeachingLanguages) {
        if (Array.isArray(teacher.teacherTeachingLanguages) && teacher.teacherTeachingLanguages.length > 0) {
          exportObject[fieldName] = teacher.teacherTeachingLanguages
            .map((item: ITeacherTeachingLanguageDto) => {
              if (item && typeof item === 'object') {
                return item.teachingLanguageName || '';
              }
              return typeof item === 'string' ? item : String(item);
            })
            .filter(lang => lang)
            .join(' | ');
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.vacationDates) {
        if (Array.isArray(teacher.vacationDates) && teacher.vacationDates.length > 0) {
          exportObject[fieldName] = teacher.vacationDates
            .map(date => {
              try {
                const dateObj = date instanceof Date ? date : new Date(date);
                return dateObj.toLocaleDateString();
              } catch (error) {
                console.warn('Invalid date format:', date);
                return String(date);
              }
            })
            .filter(date => date)
            .join(' | ');
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.activePackagesCount) {
        exportObject[fieldName] = teacher.activePackagesCount?.toString() || '0';
      }
      else if (fieldName === teacherFields.activeStudentsCount) {
        exportObject[fieldName] = teacher.activeStudentsCount?.toString() || '0';
      }
      else if (fieldName === teacherFields.accountStatus) {
        exportObject[fieldName] = teacher.accountStatus !== undefined ? String(teacher.accountStatus) : '';
      }
      else if (fieldName === teacherFields.lastAccountStatusUpdate) {
        if (teacher.lastAccountStatusUpdate) {
          try {
            const dateObj = teacher.lastAccountStatusUpdate instanceof Date
              ? teacher.lastAccountStatusUpdate
              : new Date(teacher.lastAccountStatusUpdate);
            exportObject[fieldName] = dateObj.toLocaleDateString();
          } catch (error) {
            console.warn('Invalid lastAccountStatusUpdate date format:', teacher.lastAccountStatusUpdate);
            exportObject[fieldName] = String(teacher.lastAccountStatusUpdate || '');
          }
        } else {
          exportObject[fieldName] = '';
        }
      }
      else if (fieldName === teacherFields.approvedDate) {
        if (teacher.approvedDate) {
          try {
            const dateObj = teacher.approvedDate instanceof Date
              ? teacher.approvedDate
              : new Date(teacher.approvedDate);
            exportObject[fieldName] = dateObj.toLocaleDateString();
          } catch (error) {
            console.warn('Invalid approvedDate date format:', teacher.approvedDate);
            exportObject[fieldName] = String(teacher.approvedDate || '');
          }
        } else {
          exportObject[fieldName] = '';
        }
      }
      else {
        // For any other fields, fall back to simple string conversion
        const value = teacher[fieldName as keyof typeof teacher];
        if (value !== undefined && value !== null) {
          if (typeof value === 'object' && value !== null) {
            if (Array.isArray(value)) {
              exportObject[fieldName] = value.map(item =>
                typeof item === 'object' ? JSON.stringify(item) : String(item)
              ).join(' | ');
            } else {
              exportObject[fieldName] = JSON.stringify(value);
            }
          } else {
            exportObject[fieldName] = String(value);
          }
        } else {
          exportObject[fieldName] = '';
        }
      }
    }

    return exportObject;
  }



  /**
   * Creates a default IGetTeachersRequest object with standard values
   * @param searchTeacherDtoFieldNames Optional object containing field names to customize the default sortColumn
   * @returns IGetTeachersRequest with default values
   */
  createDefaultTeachersRequest(searchTeacherDtoFieldNames?: ISearchTeacherDto): IGetTeachersRequest {
    return {
      pageNumber: 1,
      pageSize: 10,
      sortColumn: searchTeacherDtoFieldNames?.firstName || TeacherListComponentHelperService.DEFAULT_SORT_COLUMN,
      sortDirection: TeacherListComponentHelperService.DEFAULT_SORT_DIRECTION,
      searchTerm: null,
      gender: IGenderEnum.None,
      approvedDateFrom: null,
      approvedDateTo: null,
      teachingLanguage: null,
      speakingLanguage: null,
      includeBlocked: false,
      availabilityStatus: null,
      teachingAgesExperience: null,
      teacherStudentAgesPreference: null,
      studentAgesMin: 2,
      studentAgesMax: 17,
    };
  }

  /**
   * Maps URL query parameters to an IGetTeachersRequest object
   * @param params Route parameters from ActivatedRoute
   * @param defaultRequest Optional default request to use as base (will create one if not provided)
   * @returns IGetTeachersRequest populated with values from URL parameters
   */
  mapQueryParamsToTeachersRequest(params: Params, defaultRequest?: IGetTeachersRequest): IGetTeachersRequest {
    // Start with clean defaults or use provided default
    const request: IGetTeachersRequest = defaultRequest || this.createDefaultTeachersRequest();

    // Use nameOf to get type-safe property names
    const paramsMap = nameOf<IGetTeachersRequest>();

    // Dynamically map query params from `paramsMap`
    if (params[paramsMap.pageNumber] !== undefined) {
      request.pageNumber = +params[paramsMap.pageNumber];
    }
    if (params[paramsMap.pageSize] !== undefined) {
      request.pageSize = +params[paramsMap.pageSize];
    }
    if (params[paramsMap.sortColumn!] !== undefined && params[paramsMap.sortColumn!] !== 'null') {
      request.sortColumn = params[paramsMap.sortColumn!];
    }
    if (params[paramsMap.sortDirection!] !== undefined && params[paramsMap.sortDirection!] !== 'null') {
      request.sortDirection = params[paramsMap.sortDirection!] as 'asc' | 'desc';
    }
    if (params[paramsMap.searchTerm!] !== undefined && params[paramsMap.searchTerm!] !== 'null') {
      request.searchTerm = params[paramsMap.searchTerm!];
    }
    if (params[paramsMap.gender] !== undefined) {
      request.gender = +params[paramsMap.gender];
    }
    if (params[paramsMap.approvedDateFrom!] !== undefined && params[paramsMap.approvedDateFrom!] !== 'null') {
      request.approvedDateFrom = new Date(params[paramsMap.approvedDateFrom!]);
    }
    if (params[paramsMap.approvedDateTo!] !== undefined && params[paramsMap.approvedDateTo!] !== 'null') {
      request.approvedDateTo = new Date(params[paramsMap.approvedDateTo!]);
    }
    if (params[paramsMap.teachingLanguage!] !== undefined && params[paramsMap.teachingLanguage!] !== 'null') {
      request.teachingLanguage = params[paramsMap.teachingLanguage!];
    }
    if (params[paramsMap.speakingLanguage!] !== undefined && params[paramsMap.speakingLanguage!] !== 'null') {
      request.speakingLanguage = params[paramsMap.speakingLanguage!];
    }
    if (params[paramsMap.includeBlocked!] !== undefined) {
      request.includeBlocked = params[paramsMap.includeBlocked!] === 'true';
    }
    if (params[paramsMap.availabilityStatus!] !== undefined && params[paramsMap.availabilityStatus!] !== 'null') {
      request.availabilityStatus = +params[paramsMap.availabilityStatus!];
    }
    if (params[paramsMap.teachingAgesExperience!] !== undefined && params[paramsMap.teachingAgesExperience!] !== 'null') {
      request.teachingAgesExperience = +params[paramsMap.teachingAgesExperience!];
    }
    if (params[paramsMap.teacherStudentAgesPreference!] !== undefined && params[paramsMap.teacherStudentAgesPreference!] !== 'null') {
      request.teacherStudentAgesPreference = +params[paramsMap.teacherStudentAgesPreference!];
    }
    if (params[paramsMap.studentAgesMin] !== undefined) {
      request.studentAgesMin = +params[paramsMap.studentAgesMin];
    }
    if (params[paramsMap.studentAgesMax] !== undefined) {
      request.studentAgesMax = +params[paramsMap.studentAgesMax];
    }

    return request;
  }

  initializeTableColumns(): IDataGridFields[] {
    const searchTeacherDtoFieldNames = nameOf<ISearchTeacherDto>();

    return [
      { field: searchTeacherDtoFieldNames.firstName, header: 'First Name', sortable: true },
      { field: searchTeacherDtoFieldNames.lastName, header: 'Last Name', sortable: true },
      { field: searchTeacherDtoFieldNames.primaryEmail, header: 'Email', sortable: true },
      { field: searchTeacherDtoFieldNames.gender, header: 'Gender', sortable: true },
      { field: searchTeacherDtoFieldNames.country, header: 'Country', sortable: true, maxWidth: "200px" },
      { field: searchTeacherDtoFieldNames.speakingLanguages, header: 'Native Speaking Languages', sortable: false },
      // {field: searchTeacherDtoFieldNames.dateOfRegistration!, header: 'Registration Date', sortable: true},
      { field: searchTeacherDtoFieldNames.activePackagesCount, header: 'Active Packages', sortable: true },
      { field: searchTeacherDtoFieldNames.activeStudentsCount, header: 'Active Students', sortable: true },
      { field: searchTeacherDtoFieldNames.vacationDates, header: 'Vacation Days', sortable: false, maxWidth: "300px" },
      { field: searchTeacherDtoFieldNames.teacherTeachingLanguages, header: 'Teaching Languages', sortable: false },
      { field: searchTeacherDtoFieldNames.availabilityStatus, header: 'Availability Status', sortable: true },
      { field: searchTeacherDtoFieldNames.lastAccountStatusUpdate!, header: 'Last Acc Status Update', sortable: false },
      { field: searchTeacherDtoFieldNames.approvedDate!, header: 'Approved Date', sortable: true },
      { field: searchTeacherDtoFieldNames.accountStatus, header: 'Account Status', sortable: true }
    ];
  }
}
