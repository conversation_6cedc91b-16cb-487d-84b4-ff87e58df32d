# Date Range Converter Service

## Overview

The `DateRangeConverterService` provides a generic solution for converting date ranges (like `approvedDateFrom`/`approvedDateTo`) from local timezone to UTC with proper start-of-day and end-of-day handling using moment-timezone.

## Key Features

- **Timezone-aware conversion**: Converts dates from user's local timezone to UTC
- **Day boundary handling**: Automatically applies start-of-day (00:00:00) and end-of-day (23:59:59) 
- **Flexible output formats**: Returns Date objects or ISO strings
- **Generic implementation**: Works with any date range fields
- **Error handling**: Gracefully handles invalid dates and timezone errors
- **Type safety**: Full TypeScript support with proper type inference

## Basic Usage

### 1. Converting Date Ranges to UTC

```typescript
import { DateRangeConverterService } from 'SharedModules.Library';

constructor(private dateRangeConverter = inject(DateRangeConverterService)) {}

// Convert date range to UTC Date objects
const result = this.dateRangeConverter.convertDateRangeToUtc(
  new Date('2024-01-15'), // fromDate
  new Date('2024-01-20'), // toDate
);
// result.fromDate: 2024-01-15 00:00:00 UTC
// result.toDate: 2024-01-20 23:59:59 UTC

// Convert to ISO strings for API requests
const apiResult = this.dateRangeConverter.convertDateRangeToUtc(
  fromDate,
  toDate,
  { outputFormat: 'string' }
);
// result.fromDate: "2024-01-15T00:00:00.000Z"
// result.toDate: "2024-01-20T23:59:59.999Z"
```

### 2. Converting Request Objects

```typescript
// Convert specific date range fields in a request object
const cleanedRequest = this.dateRangeConverter.convertDateRangeInRequest(
  teachersRequest,
  'approvedDateFrom',
  'approvedDateTo',
  { outputFormat: 'string' }
);
```

### 3. Custom Timezone and Options

```typescript
// Custom timezone and no day boundary adjustment
const customResult = this.dateRangeConverter.convertDateRangeToUtc(
  fromDate,
  toDate,
  {
    sourceTimezone: 'America/New_York',
    applyStartOfDay: false,
    applyEndOfDay: false,
    outputFormat: 'date'
  }
);
```

## Implementation in Teachers List

### TeacherListComponentHelperService

The helper service now includes a method specifically for converting teacher request date ranges:

```typescript
/**
 * Converts date range fields in a teachers request to UTC for API submission
 */
convertDateRangeToUtcForApi(request: IGetTeachersRequest): IGetTeachersRequest {
  return this.dateRangeConverter.convertDateRangeInRequest(
    request,
    'approvedDateFrom',
    'approvedDateTo',
    {
      outputFormat: 'string',
      applyStartOfDay: true,
      applyEndOfDay: true
    }
  );
}
```

### TeachersListComponent

The main component automatically applies date range conversion in the `cleanRequestForApi` method:

```typescript
protected override cleanRequestForApi<T extends IBasedDataGridRequest>(request: T): Partial<T> {
  let processedRequest = { ...request };

  // Apply date range conversion for teachers requests
  if (this.isTeachersRequest(processedRequest)) {
    processedRequest = this.teacherHelperService.convertDateRangeToUtcForApi(
      processedRequest as IGetTeachersRequest
    ) as unknown as T;
  }

  // ... rest of cleaning logic
}
```

## Configuration Options

### IDateRangeConversionOptions

```typescript
interface IDateRangeConversionOptions {
  /** Source timezone for input dates (defaults to user's current timezone) */
  sourceTimezone?: string;
  
  /** Apply start of day to 'from' date (defaults to true) */
  applyStartOfDay?: boolean;
  
  /** Apply end of day to 'to' date (defaults to true) */
  applyEndOfDay?: boolean;
  
  /** Output format: 'date' or 'string' (defaults to 'date') */
  outputFormat?: 'date' | 'string';
}
```

## Date Range Behavior

### Default Behavior (Recommended for API Requests)

- **From Date**: Converted to start of day (00:00:00) in source timezone, then to UTC
- **To Date**: Converted to end of day (23:59:59) in source timezone, then to UTC

### Example

User selects: January 15, 2024 to January 20, 2024 (in EST timezone)

**Result:**
- `fromDate`: "2024-01-15T05:00:00.000Z" (00:00:00 EST → UTC)
- `toDate`: "2024-01-21T04:59:59.999Z" (23:59:59 EST → UTC)

## Error Handling

The service includes comprehensive error handling:

```typescript
// Gracefully handles null/undefined dates
const result = this.dateRangeConverter.convertDateRangeToUtc(null, undefined);
// result.fromDate: null, result.toDate: null

// Handles invalid date formats
const result = this.dateRangeConverter.convertDateRangeToUtc('invalid-date', validDate);
// result.fromDate: null, result.toDate: converted validDate
// Console warning logged for invalid date
```

## Validation

```typescript
// Validate that fromDate <= toDate
const isValid = this.dateRangeConverter.validateDateRange(fromDate, toDate);
```

## Migration from Current Implementation

### Before (Manual Conversion)
```typescript
// Old manual approach in cleanRequestForApi
if ((key === 'approvedDateFrom' || key === 'approvedDateTo') && value instanceof Date) {
  const isoString = value.toISOString();
  cleanedRequest[key] = isoString;
}
```

### After (Using DateRangeConverterService)
```typescript
// New approach - automatic timezone-aware conversion
if (this.isTeachersRequest(processedRequest)) {
  processedRequest = this.teacherHelperService.convertDateRangeToUtcForApi(processedRequest);
}
```

## Benefits

1. **Timezone Accuracy**: Proper timezone conversion instead of naive `.toISOString()`
2. **Day Boundary Handling**: Automatic start/end of day application
3. **Reusability**: Generic service can be used for any date range fields
4. **Type Safety**: Full TypeScript support with proper type inference
5. **Error Resilience**: Graceful handling of invalid dates and timezones
6. **Consistency**: Standardized approach across all date range filters

## Future Extensions

The service can easily be extended for other date range scenarios:

- Student registration date ranges (`registeredFrom`/`registeredTo`)
- Lesson date ranges
- Package activation date ranges
- Any other date range filtering needs

Simply use the same pattern:

```typescript
const convertedRequest = this.dateRangeConverter.convertDateRangeInRequest(
  request,
  'dateFieldFrom',
  'dateFieldTo',
  options
);
```
